# 提示词: 生成 vueGLM 企业管理 (EnterpriseManagementV2.vue) 页面

## 第一部分: 核心原则与目标

1.  **目标**: 创建一个功能完备、样式精美、交互友好的企业管理页面 (`EnterpriseManagementV2.vue`)。
2.  **技术栈**: Vue 3 (Composition API, `<script setup>`), TypeScript, Element Plus。
3.  **代码风格**: 遵循项目现有代码风格, 确保代码清晰、可维护性高。
4.  **数据模拟**: 所有后端数据请求都必须通过一个独立的 API 文件 (`vueGLM/src/api/enterpriseV2.ts`) 来模拟, **不得**引入第三方库。
5.  **像素级还原**: 最终生成的页面在布局、样式、组件属性上应与本提示词描述高度一致。

---

## 第二部分: API 层 (`vueGLM/src/api/enterpriseV2.ts`)

### 1. 接口定义

```typescript
// vueGLM/src/api/enterpriseV2.ts

export interface EnterpriseItem {
  id: string;
  enterpriseName: string; // 企业名称
  creditCode: string; // 社会统一信用代码
  industry: '农业' | '物流' | '仓储' | '加工'; // 所属行业
  contactPerson: string;
  contactPhone: string;
  establishmentDate: string; // 成立日期 YYYY-MM-DD
  status: 'active' | 'inactive'; // 启用/停用
  auditStatus: 'pending' | 'approved' | 'rejected'; // 待审核, 已通过, 已驳回
}

export interface SearchParams {
  enterpriseName?: string;
  creditCode?: string;
  status?: '' | 'active' | 'inactive';
  auditStatus?: '' | 'pending' | 'approved' | 'rejected';
  page: number;
  pageSize: number;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}
```

### 2. 模拟数据与 API 函数

*   创建 `mockEnterpriseDatabase` 模拟数据库, 数据需多样化。
*   实现 `fetchEnterpriseList(params: SearchParams)`, `getEnterpriseDetail(id)`, `createEnterprise(data)`, `updateEnterprise(data)`, `deleteEnterprise(id)`, `updateEnterpriseStatus(id, status)` 等全套模拟 API 函数。

---

## 第三部分: 模板层 (`EnterpriseManagementV2.vue`)

### 1. 整体布局

*   根元素 `<div class="enterprise-management-v2">`。
*   主容器 `<el-card>`。
*   **卡片头部**: 标题 "企业管理", 右侧 "新增企业"(`type="primary"`) 和 "导出" 按钮。
*   **筛选区域**: `<el-form :inline="true">`
    *   `企业名称`, `信用代码`, `状态(启用/停用)`, `审核状态`。
    *   "查询" (`type="primary"`) 和 "重置" 按钮。
*   **数据表格**: `<el-table>` `border` `stripe`
    *   `type="selection"`, `type="index"`
    *   `企业名称`, `信用代码`, `所属行业`, `联系人`, `联系电话`, `成立时间`, `状态`(el-tag), `审核状态`(el-tag)。
    *   `操作` (`fixed="right"`):
        *   "详情", "编辑"。
        *   "审核" (`v-if="pending"`), "重新审核" (`v-if="rejected"`)。
        *   "启用" (`v-if="inactive"`), "停用" (`v-if="active"`)。
        *   "删除"。
*   **分页**: `<el-pagination>`, 居右。
*   **新增/编辑/审核 对话框**: 根据不同场景弹出相应表单或详情的对话框。

---

## 第四部分: 脚本层 (`<script setup>`)

*   实现标准列表页的全部逻辑: 数据加载, 搜索, 重置, 分页。
*   实现新增/编辑/删除/审核/状态变更等操作的完整对话框与按钮交互逻辑。
*   包含状态文本和样式的转换辅助函数。

---

## 第五部分: 样式层 (`<style scoped>`)

```css
.enterprise-management-v2 { padding: 20px; }
.search-section { margin-bottom: 20px; padding: 20px; background-color: #f8f9fa; border-radius: 4px; }
.pagination-wrapper { margin-top: 20px; display: flex; justify-content: flex-end; }
```

---

## 第六部分: 路由配置 (`vueGLM/src/router/index.ts`)

```typescript
{
  path: '/enterprise/management-v2',
  name: 'EnterpriseManagementV2',
  component: () => import('@/views/enterprise/EnterpriseManagementV2.vue'),
  meta: { title: '企业管理V2' }
}
```
