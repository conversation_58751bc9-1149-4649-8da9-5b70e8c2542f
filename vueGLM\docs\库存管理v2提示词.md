# 提示词: 生成 vueGLM 库存管理模块 V2

## 第一部分: 核心原则与目标

1.  **目标**: 创建功能完备的库存管理模块, 包含 **库存列表** (`InventoryListV2.vue`) 和 **库存流水** (`InventoryFlowV2.vue`) 两个核心页面。
2.  **技术栈**: Vue 3 (Composition API, `<script setup>`), TypeScript, Element Plus, ECharts, Vue Router。
3.  **代码风格**: 遵循项目现有代码风格。
4.  **数据模拟**: 所有后端数据请求都必须通过一个独立的 API 文件 (`vueGLM/src/api/inventoryV2.ts`) 来模拟, **不得**引入第三方库。
5.  **像素级还原**: 最终生成的页面应与本提示词描述高度一致。

---

## 第二部分: API 层 (`vueGLM/src/api/inventoryV2.ts`)

### 1. 接口定义

```typescript
// vueGLM/src/api/inventoryV2.ts

export interface InventoryItem {
  id: string;
  productName: string;
  specification: string;
  warehouseName: string;
  quantity: number;
  unit: string;
  status: 'in-stock' | 'low-stock' | 'out-of-stock';
  updateTime: string;
}

export interface InventoryFlowItem {
    id: string;
    productId: string;
    productName: string;
    changeType: 'inbound' | 'outbound' | 'adjustment'; // 入库/出库/调整
    changeQuantity: number; // >0 为增加, <0 为减少
    beforeQuantity: number;
    afterQuantity: number;
    operator: string;
    changeTime: string;
    remark?: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}
```

### 2. 模拟数据与 API 函数

*   创建 `mockInventoryDatabase` 和 `mockInventoryFlowDatabase` 模拟数据库。
*   实现 `fetchInventoryList(params)`, `getInventoryDetail(id)`, `fetchInventoryFlowList(params)` 等模拟 API 函数。

---

## 第三部分: 库存列表页面 (`InventoryListV2.vue`)

### 1. 模板

*   **主容器**: `<el-card>`。
*   **数据概览**: `<el-row :gutter="20">` 展示 SKU 总数、总库存量、低库存预警等 KPI 卡片。
*   **筛选区域**: `产品名称`, `仓库`, `库存状态`。
*   **数据表格**: `<el-table>` `border` `stripe`
    *   `产品名称`, `规格`, `仓库`, `库存数量`, `单位`, `状态` (el-tag), `更新时间`, `操作`。
    *   `操作`: "查看流水"。
*   **分页**: `<el-pagination>`, 居右。

---

## 第四部分: 库存流水页面 (`InventoryFlowV2.vue`)

### 1. 模板

*   **页面头部**: `<el-page-header @back="goBack">` 显示产品名称的库存流水。
*   **主容器**: `<el-card>`。
*   **筛选区域**: `变动类型`, `操作人员`, `变动时间`。
*   **数据表格**: `<el-table>` `border` `stripe`
    *   `变动时间`, `变动类型`, `变动数量`, `变动前数量`, `变动后数量`, `操作人员`, `备注`。
*   **分页**: `<el-pagination>`, 居右。

---

## 第五部分: 样式层 (`<style scoped>`)

```css
.page-container { padding: 20px; }
.kpi-card { margin-bottom: 20px; }
.search-section { margin-bottom: 20px; }
.pagination-wrapper { margin-top: 20px; display: flex; justify-content: flex-end; }
```

---

## 第六部分: 路由配置 (`vueGLM/src/router/index.ts`)

```typescript
{
  path: '/inventory/list-v2',
  name: 'InventoryListV2',
  component: () => import('@/views/inventory/InventoryListV2.vue'),
  meta: { title: '库存列表V2' }
},
{
  path: '/inventory/flow-v2/:productId',
  name: 'InventoryFlowV2',
  component: () => import('@/views/inventory/InventoryFlowV2.vue'),
  meta: { title: '库存流水V2' }
}
```