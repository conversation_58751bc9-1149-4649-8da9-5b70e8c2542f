<template>
  <div class="warehouse-txn-management-v2-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>仓库交易管理 v2.0</span>
          <div class="header-actions">
            <el-button type="primary" @click="handleCreateWarehouseTxn('in')">
              <el-icon><Plus /></el-icon>创建入库单
            </el-button>
            <el-button type="primary" @click="handleCreateWarehouseTxn('out')">
              <el-icon><Plus /></el-icon>创建出库单
            </el-button>
            <el-button type="success" @click="handleExport">
              <el-icon><Download /></el-icon>导出
            </el-button>
          </div>
        </div>
      </template>

      <!-- 筛选区域 -->
      <div class="filter-section">
        <el-form :model="searchForm" inline>
          <el-form-item label="单据编号">
            <el-input v-model="searchForm.documentNo" placeholder="请输入单据编号" clearable />
          </el-form-item>
          <el-form-item label="交易类型">
            <el-select v-model="searchForm.transactionType" placeholder="请选择交易类型" clearable style="width: 200px">
              <el-option label="入库" value="in" />
              <el-option label="出库" value="out" />
            </el-select>
          </el-form-item>
          <el-form-item label="仓库">
            <el-select v-model="searchForm.warehouseId" placeholder="请选择仓库" clearable style="width: 200px">
              <el-option v-for="warehouse in warehouseOptions" :key="warehouse.id" :label="warehouse.name" :value="warehouse.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 200px">
              <el-option label="待审核" value="pending" />
              <el-option label="已审核" value="approved" />
              <el-option label="已拒绝" value="rejected" />
              <el-option label="已完成" value="completed" />
              <el-option label="已取消" value="cancelled" />
            </el-select>
          </el-form-item>
          <el-form-item label="交易时间">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              @change="handleDateRangeChange"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 仓库交易列表 -->
      <el-table
        :data="warehouseTxnList"
        style="width: 100%"
        v-loading="loading"
        stripe
        border
      >
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="documentNo" label="单据编号" min-width="120" show-overflow-tooltip />
        <el-table-column prop="transactionType" label="交易类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getTransactionTypeTag(row.transactionType)">
              {{ getTransactionTypeLabel(row.transactionType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="warehouseName" label="仓库" min-width="120" show-overflow-tooltip />
        <el-table-column prop="partnerName" label="供应商/客户" min-width="120" show-overflow-tooltip />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTag(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="transactionTime" label="交易时间" width="180" />
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">详情</el-button>
            <el-button type="success" size="small" @click="handleEdit(row)" v-if="row.status === 'pending'">编辑</el-button>
            <el-button type="warning" size="small" @click="handleAudit(row)" v-if="row.status === 'pending'">审核</el-button>
            <el-button type="info" size="small" @click="handleViewProof(row)" v-if="row.proofUrl">查看凭证</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handlePageSizeChange"
          @current-change="handleCurrentPageChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑仓库交易对话框 -->
    <el-dialog
      v-model="warehouseTxnDialog.visible"
      :title="warehouseTxnDialog.title"
      width="800px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-form
        ref="warehouseTxnFormRef"
        :model="warehouseTxnForm"
        :rules="warehouseTxnFormRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="交易类型" prop="transactionType">
              <el-select v-model="warehouseTxnForm.transactionType" placeholder="请选择交易类型" style="width: 100%" :disabled="warehouseTxnDialog.mode === 'edit'">
                <el-option label="入库" value="in" />
                <el-option label="出库" value="out" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="仓库" prop="warehouseId">
              <el-select v-model="warehouseTxnForm.warehouseId" placeholder="请选择仓库" style="width: 100%">
                <el-option v-for="warehouse in warehouseOptions" :key="warehouse.id" :label="warehouse.name" :value="warehouse.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="warehouseTxnForm.transactionType === 'in' ? '供应商' : '客户'" prop="partnerId">
              <el-select 
                v-model="warehouseTxnForm.partnerId" 
                :placeholder="warehouseTxnForm.transactionType === 'in' ? '请选择供应商' : '请选择客户'" 
                style="width: 100%"
              >
                <el-option 
                  v-for="partner in warehouseTxnForm.transactionType === 'in' ? supplierOptions : customerOptions" 
                  :key="partner.id" 
                  :label="partner.name" 
                  :value="partner.id" 
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="交易时间" prop="transactionTime">
              <el-date-picker
                v-model="warehouseTxnForm.transactionTime"
                type="datetime"
                placeholder="选择交易时间"
                style="width: 100%"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="操作人" prop="operator">
              <el-input v-model="warehouseTxnForm.operator" placeholder="请输入操作人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预计完成时间" prop="estimatedCompletionTime">
              <el-date-picker
                v-model="warehouseTxnForm.estimatedCompletionTime"
                type="datetime"
                placeholder="选择预计完成时间"
                style="width: 100%"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 商品明细 -->
        <el-form-item label="商品明细">
          <div class="items-container">
            <div class="items-header">
              <span>商品列表</span>
              <el-button type="primary" size="small" @click="addWarehouseTxnItem">
                <el-icon><Plus /></el-icon>添加商品
              </el-button>
            </div>
            
            <el-table :data="warehouseTxnForm.items" style="width: 100%">
              <el-table-column prop="productName" label="商品名称" min-width="150">
                <template #default="{ row, $index }">
                  <el-select
                    v-model="row.productId"
                    placeholder="请选择商品"
                    style="width: 100%"
                    @change="(val) => handleProductChange(val, $index)"
                  >
                    <el-option v-for="product in productOptions" :key="product.id" :label="product.name" :value="product.id" />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column prop="specification" label="规格" width="120">
                <template #default="{ row }">
                  <el-input v-model="row.specification" placeholder="请输入规格" />
                </template>
              </el-table-column>
              <el-table-column prop="batchNo" label="批次号" width="120">
                <template #default="{ row }">
                  <el-input v-model="row.batchNo" placeholder="请输入批次号" />
                </template>
              </el-table-column>
              <el-table-column prop="quantity" label="数量" width="100">
                <template #default="{ row }">
                  <el-input-number v-model="row.quantity" :min="1" style="width: 100%" />
                </template>
              </el-table-column>
              <el-table-column prop="unit" label="单位" width="80">
                <template #default="{ row }">
                  <el-select v-model="row.unit" placeholder="单位" style="width: 100%">
                    <el-option label="千克" value="千克" />
                    <el-option label="吨" value="吨" />
                    <el-option label="箱" value="箱" />
                    <el-option label="袋" value="袋" />
                    <el-option label="件" value="件" />
