# 提示词: 生成 vueGLM 政策管理模块 V2

## 第一部分: 核心原则与目标

1.  **目标**: 创建一个功能完备、前后端分离的政策管理模块。该模块包含四个核心页面: 政策列表、政策详情、补贴申请、我的申请。
2.  **技术栈**: Vue 3 (Composition API, `<script setup>`), TypeScript, Element Plus, Vue Router。
3.  **代码风格**: 遵循项目现有代码风格, 确保代码清晰、可维护性高。
4.  **数据模拟**: 所有后端数据请求都必须通过一个独立的 API 文件 (`vueGLM/src/api/policyV2.ts`) 来模拟。**严禁**在 Vue 组件内部直接编写 mock 数据, **不得**引入第三方库。
5.  **像素级还原**: 最终生成的页面在布局、样式、组件属性上应与本提示词描述高度一致。

---

## 第二部分: API 层 (`vueGLM/src/api/policyV2.ts`)

### 1. 接口定义

```typescript
// vueGLM/src/api/policyV2.ts

export interface PolicyItem {
  id: string;
  title: string;
  type: '国家级' | '省级' | '市级' | '区县级';
  issuingAuthority: string; // 发布单位
  publishDate: string; // YYYY-MM-DD
  status: 'active' | 'expired' | 'draft'; // active-有效, expired-已失效, draft-草稿
  content: string; // 富文本内容
  files?: { name: string; url: string }[];
}

export interface SubsidyApplication {
  id: string;
  applicationNumber: string;
  policyId: string;
  policyTitle: string;
  applicantName: string;
  contactPhone: string;
  projectName: string;
  amount: number;
  reason: string;
  applicationDate: string; // YYYY-MM-DD HH:mm:ss
  status: 'draft' | 'submitted' | 'approved' | 'rejected' | 'withdrawn';
  materials?: { name: string; url: string }[];
  approvalComment?: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}
```

### 2. 模拟数据与 API 函数

*   创建 `mockPolicyDatabase` 和 `mockApplicationDatabase` 两个模拟数据库, 分别包含约 30 条政策和 20 条申请记录, 数据需多样化。
*   实现以下所有 API 函数的模拟版本, 包含筛选、分页和延时逻辑：
    *   `fetchPolicyList(params)`
    *   `getPolicyDetail(id)`
    *   `createPolicy(data)`
    *   `updatePolicy(data)`
    *   `deletePolicy(id)`
    *   `fetchSubsidyApplicationList(params)`
    *   `getSubsidyApplicationDetail(id)`
    *   `createSubsidyApplication(data)`
    *   `updateSubsidyApplication(data)`
    *   `withdrawApplication(id)`

---

## 第三部分: 政策列表页面 (`PolicyListV2.vue`)

### 1. 模板

*   **主容器**: `<el-card>`。
*   **卡片头部**: 标题 "政策列表", 右侧有 "发布政策" (`type="primary"`) 和 "导出" 按钮。
*   **筛选区域**: `<el-form :inline="true">`
    *   `政策标题`: `el-input`, `clearable`
    *   `政策类型`: `el-select`, `clearable`, Options: 国家级, 省级, 市级, 区县级
    *   `状态`: `el-select`, `clearable`, Options: 有效, 已失效, 草稿
    *   "查询" (`type="primary"`) 和 "重置" 按钮。
*   **数据表格**: `<el-table>` `border` `stripe`
    *   `type="selection"`
    *   `type="index"`, `label="序号"`
    *   `政策标题` (`prop="title"`)
    *   `政策类型` (`prop="type"`)
    *   `发布单位` (`prop="issuingAuthority"`)
    *   `发布时间` (`prop="publishDate"`)
    *   `状态`: 使用 `el-tag` (有效-success, 已失效-info, 草稿-warning)。
    *   `操作`: `fixed="right"`, 包含 "详情", "编辑", "删除", "查看申请" `el-button` (`link`, `type="primary"`).
*   **分页**: `<el-pagination>`, 居右。

### 2. 脚本

*   实现 `loadData`, `handleSearch`, `resetSearch`, 分页处理等标准列表页逻辑。
*   "详情" 按钮通过 `router.push` 跳转到详情页。
*   其他操作按钮暂时只做 `ElMessage` 提示。

---

## 第四部分: 政策详情页面 (`PolicyDetailV2.vue`)

### 1. 模板

*   **主容器**: `<div class="policy-detail-v2">`。
*   **页面头部**: `<el-page-header @back="goBack">`, content 为 "政策详情"。
*   **信息卡片**: `<el-card v-if="policy">`
    *   卡片头部为居中的 `<h2>{{ policy.title }}</h2>`。
    *   使用 `<el-descriptions :column="2" border>` 展示类型、发布单位、日期、状态。
    *   使用 `v-html` 展示政策富文本内容。
    *   附件列表: 遍历 `policy.files` 显示可下载的链接。
    *   卡片底部为居中的 "申请补贴" (`type="primary"`) 和 "返回列表" 按钮。
*   **空状态**: `<el-empty v-else-if="!loading">`。

### 2. 脚本

*   通过 `useRoute().params.id` 获取政策 ID。
*   `onMounted` 时调用 `getPolicyDetail` 加载数据。
*   "申请补贴" 按钮跳转到申请页, 并通过 query 参数传递 `policyId`。

---

## 第五部分: 补贴申请页面 (`SubsidyApplicationV2.vue`)

### 1. 模板

*   **页面头部**: `<el-page-header @back="goBack">`, content 为 "补贴申请"。
*   **表单卡片**: `<el-card>`
    *   头部标题: `申请：{{ policyTitle }}`。
    *   `<el-form>` `label-width="120px"`:
        *   `申请人/单位`, `联系电话`, `申请项目名称` (`el-input`)
        *   `申请金额(元)` (`el-input-number`)
        *   `申请理由` (`el-input type="textarea"`)
        *   `上传申请材料`: `<el-upload>` (仅做展示, 不实现真上传)。
        *   "保存草稿" (`type="primary"`), "提交申请" (`type="success"`), "取消" 按钮。

### 2. 脚本

*   `onMounted` 时通过 `useRoute().query.policyId` 获取 `policyId` 并加载政策标题。
*   实现表单数据绑定 (`form`) 和校验规则 (`rules`)。
*   `submitForm(isSubmitted)`: 根据参数决定是保存草稿还是提交, 调用 `createSubsidyApplication`, 成功后跳转到 "我的申请" 页面。

---

## 第六部分: 我的申请页面 (`MyApplicationsV2.vue`)

### 1. 模板

*   **主容器**: `<el-card>`。
*   **卡片头部**: 标题 "我的申请", 右侧 "新建申请" (`type="primary"`) 按钮。
*   **筛选区域**: `el-select` 用于筛选申请状态。
*   **数据表格**: `<el-table>` `border` `stripe`
    *   `申请编号`, `政策名称`, `申请金额`, `状态`, `申请时间`。
    *   `操作` (`fixed="right"`): 包含 "详情", "编辑"(`v-if="draft"`), "撤回"(`v-if="draft || submitted"`), "查看审批意见"(`v-if="rejected"`)。
*   **分页**: `<el-pagination>`, 居右。

### 2. 脚本

*   实现标准列表页逻辑。
*   "新建申请" 按钮跳转到政策列表页, 引导用户选择政策。
*   各种操作按钮仅做 `ElMessage` 提示。

---

## 第七部分: 路由配置 (`vueGLM/src/router/index.ts`)

在 `/policy` 子路由下添加 V2 路由:

```typescript
// V2 Routes
{
  path: 'list-v2',
  name: 'PolicyListV2',
  component: () => import('@/views/policy/PolicyListV2.vue')
},
{
  path: 'detail-v2/:id',
  name: 'PolicyDetailV2',
  component: () => import('@/views/policy/PolicyDetailV2.vue')
},
{
  path: 'application-v2/:mode', // mode can be 'new' or an application id
  name: 'SubsidyApplicationV2',
  component: () => import('@/views/policy/SubsidyApplicationV2.vue')
},
{
  path: 'my-applications-v2',
  name: 'MyApplicationsV2',
  component: () => import('@/views/policy/MyApplicationsV2.vue')
}
```