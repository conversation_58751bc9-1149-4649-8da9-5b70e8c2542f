# 提示词: 生成 vueGLM 应急管理 V2

## 第一部分: 核心原则与目标

1.  **目标**: 创建一个功能完备的应急事件管理页面 (`EmergencyManagementV2.vue`)。
2.  **技术栈**: Vue 3 (Composition API, `<script setup>`), TypeScript, Element Plus, Vue Router。
3.  **代码风格**: 遵循项目现有代码风格。
4.  **数据模拟**: 所有后端数据请求都必须通过一个独立的 API 文件 (`vueGLM/src/api/emergencyV2.ts`) 来模拟, **不得**引入第三方库。
5.  **像素级还原**: 最终生成的页面应与本提示词描述高度一致。

---

## 第二部分: API 层 (`vueGLM/src/api/emergencyV2.ts`)

### 1. 接口定义

```typescript
// vueGLM/src/api/emergencyV2.ts

export interface EmergencyEvent {
  id: string;
  eventName: string;
  type: '设备故障' | '自然灾害' | '安全事故';
  level: '一般' | '较大' | '重大' | '特别重大';
  status: 'pending' | 'processing' | 'completed'; // 待处理, 处理中, 已完成
  region: string; // 事发区域
  occurrenceTime: string; // 发生时间
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}
```

### 2. 模拟数据与 API 函数

*   创建 `mockEmergencyDatabase` 模拟数据库。
*   实现 `fetchEmergencyList(params)`, `getEmergencyDetail(id)`, `createEmergencyEvent(data)`, `updateEmergencyStatus(id, status)` 等模拟 API 函数。

---

## 第三部分: 应急事件管理页面 (`EmergencyManagementV2.vue`)

### 1. 模板

*   **主容器**: `<el-card>`。
*   **卡片头部**: 标题 "应急事件管理", 右侧 "上报事件"(`type="primary"`) 按钮。
*   **筛选区域**: `事件类型`, `事件级别`, `状态`, `发生时间`。
*   **数据表格**: `<el-table>` `border` `stripe`
    *   `事件名称`, `类型`, `级别`(el-tag), `事发区域`, `发生时间`, `状态`(el-tag), `操作`。
    *   `操作`: "详情", "处理" (`v-if="pending"`), "完成" (`v-if="processing"`)。
*   **分页**: `<el-pagination>`, 居右。
*   **上报/详情 对话框**: 包含事件信息的表单或描述列表。

---

## 第四部分: 脚本层 (`<script setup>`)

*   实现标准列表页的全部逻辑: 数据加载, 搜索, 重置, 分页。
*   实现上报/处理等操作的对话框与按钮交互逻辑。
*   包含级别和状态的文本与样式转换辅助函数。

---

## 第五部分: 样式层 (`<style scoped>`)

```css
.page-container { padding: 20px; }
.search-section { margin-bottom: 20px; }
.pagination-wrapper { margin-top: 20px; display: flex; justify-content: flex-end; }
```

---

## 第六部分: 路由配置 (`vueGLM/src/router/index.ts`)

```typescript
{
  path: '/emergency/management-v2',
  name: 'EmergencyManagementV2',
  component: () => import('@/views/emergency/EmergencyManagementV2.vue'),
  meta: { title: '应急管理V2' }
}
```