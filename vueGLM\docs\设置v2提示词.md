# 提示词: 生成 vueGLM 系统设置页面 V2

## 第一部分: 核心原则与目标

1.  **目标**: 创建一个功能完备、模块化的系统设置页面 (`SettingsV2.vue`)。
2.  **技术栈**: Vue 3 (Composition API, `<script setup>`), TypeScript, Element Plus。
3.  **代码风格**: 遵循项目现有代码风格。
4.  **数据模拟**: 所有后端数据请求都必须通过一个独立的 API 文件 (`vueGLM/src/api/settingsV2.ts`) 来模拟, **不得**引入第三方库。
5.  **像素级还原**: 最终生成的页面应与本提示词描述高度一致。

---

## 第二部分: API 层 (`vueGLM/src/api/settingsV2.ts`)

### 1. 接口定义

```typescript
// vueGLM/src/api/settingsV2.ts

export interface SystemSettings {
  siteName: string;
  siteLogoUrl: string;
  copyrightInfo: string;
}

export interface SecuritySettings {
    loginRetryLimit: number; // 登录重试次数
    sessionTimeout: number; // 会话超时时间 (分钟)
}

export interface NotificationSettings {
    emailEnabled: boolean;
    smsEnabled: boolean;
    systemMessageEnabled: boolean;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}
```

### 2. 模拟数据与 API 函数

*   创建 `mockSystemSettings`, `mockSecuritySettings`, `mockNotificationSettings` 模拟数据库。
*   实现 `fetchSettings()` 和 `updateSettings(data)` 等模拟 API 函数。

---

## 第三部分: 系统设置页面 (`SettingsV2.vue`)

### 1. 模板

*   **主容器**: `<div class="settings-v2">`。
*   **标签页**: `<el-tabs tab-position="left">`
    *   `<el-tab-pane label="基本设置">`:
        *   包含 `系统名称`, `系统Logo` (上传), `版权信息` 的 `<el-form>`。
    *   `<el-tab-pane label="安全设置">`:
        *   包含 `登录重试次数`, `会话超时时间` 的 `<el-form>`。
    *   `<el-tab-pane label="通知设置">`:
        *   包含 `邮件通知`, `短信通知`, `站内信通知` 的 `<el-switch>` 控件。
*   **保存按钮**: 在所有标签页内容下方有一个统一的 "保存设置" (`type="primary"`) 按钮。

---

## 第四部分: 脚本层 (`<script setup>`)

*   使用 `reactive` 为每个设置模块创建独立的表单数据对象。
*   `onMounted` 时调用 `fetchSettings` 加载所有设置项。
*   `handleSave` 函数调用 `updateSettings` 保存所有模块的设置, 并给出成功提示。

---

## 第五部分: 样式层 (`<style scoped>`)

```css
.settings-v2 {
  padding: 20px;
}
.el-tab-pane {
  padding: 20px;
}
.save-button-container {
  margin-top: 20px;
  text-align: center;
}
```

---

## 第六部分: 路由配置 (`vueGLM/src/router/index.ts`)

```typescript
{
  path: '/settings-v2',
  name: 'SettingsV2',
  component: () => import('@/views/settings/SettingsV2.vue'),
  meta: { title: '系统设置V2' }
}
```