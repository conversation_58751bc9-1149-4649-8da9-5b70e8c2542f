# 提示词: 生成 vueGLM 仓库监控仪表盘 V2

## 第一部分: 核心原则与目标

1.  **目标**: 创建一个功能强大、数据可视化的仓库监控仪表盘页面 (`WarehouseDashboardV2.vue`)。
2.  **技术栈**: Vue 3 (Composition API, `<script setup>`), TypeScript, Element Plus, ECharts。
3.  **代码风格**: 遵循项目现有代码风格, 确保代码清晰、可维护性高。
4.  **数据模拟**: 所有后端数据请求都必须通过一个独立的 API 文件 (`vueGLM/src/api/warehouseMonitorV2.ts`) 来模拟, **不得**引入第三方库。
5.  **像素级还原**: 最终生成的页面在布局、样式、组件属性上应与本提示词描述高度一致。

---

## 第二部分: API 层 (`vueGLM/src/api/warehouseMonitorV2.ts`)

### 1. 接口定义

```typescript
// vueGLM/src/api/warehouseMonitorV2.ts

export interface WarehouseInfo {
  id: string;
  name: string;
  status: 'normal' | 'alert' | 'offline';
  temperature: number;
  humidity: number;
  alertCount: number;
}

export interface AlertInfo {
  id: string;
  warehouseName: string;
  type: '高温报警' | '湿度异常' | '设备离线';
  time: string;
}

export interface ChartDataPoint {
  time: string; // HH:mm
  value: number;
}

export interface MonitorData {
  totalWarehouses: number;
  onlineCount: number;
  alertCount: number;
  avgTemperature: number;
  warehouseList: WarehouseInfo[];
  alertList: AlertInfo[];
  temperatureHistory: ChartDataPoint[];
  humidityHistory: ChartDataPoint[];
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}
```

### 2. 模拟数据与 API 函数

*   创建模拟数据函数 `generateMockData()` 生成符合 `MonitorData` 接口的完整数据。
*   实现 `fetchMonitorData(): Promise<ApiResponse<MonitorData>>` 函数, 模拟获取整个仪表盘所需的数据。

---

## 第三部分: 模板层 (`WarehouseDashboardV2.vue`)

### 1. 整体布局

*   根元素为 `<div class="warehouse-dashboard-v2">`。
*   使用 `<el-row :gutter="20">` 和 `<el-col>` 进行栅格布局。
*   **左侧列 (`:span="18"`)**: 包含关键指标、温湿度图表、仓库列表。
*   **右侧列 (`:span="6"`)**: 包含告警信息列表。

### 2. 左侧列布局

*   **关键指标**: `<el-row :gutter="20">` 包含四个 `<el-col :span="6">`。
    *   每个 `<el-col>` 内是一个 `<el-card>` 用于显示：仓库总数、在线数量、告警总数、平均温度。
*   **图表卡片**: 一个 `<el-card>` 包含两个 ECharts 图表容器 (`<div>`)。
    *   `id="temp-chart"`: 用于渲染温度历史曲线。
    *   `id="humidity-chart"`: 用于渲染湿度历史曲线。
*   **仓库列表卡片**: `<el-card>`
    *   卡片头部为 "仓库实时状态"。
    *   `<el-table>` 展示 `warehouseList` 数据, 列: `仓库名称`, `状态` (el-tag), `温度(℃)`, `湿度(%)`, `今日告警`, `操作` (详情按钮)。

### 3. 右侧列布局

*   **告警信息卡片**: `<el-card>`
    *   卡片头部为 "实时告警"。
    *   使用 `<el-timeline>` 遍历 `alertList` 数据, 每个 `<el-timeline-item>` 显示告警的仓库、类型和时间。

---

## 第四部分: 脚本层 (`<script setup>`)

1.  **Imports**: 引入 Vue, Element Plus, ECharts, 和 API 文件。
2.  **State Management**: `loading`, `monitorData` 等响应式状态。
3.  **ECharts**:
    *   `initCharts()`: 在 `onMounted` 中调用, 初始化温湿度图表, 配置 `option` 并使用 `echarts.init().setOption()`。
    *   图表 `option` 需包含 `xAxis`, `yAxis`, `series`, `tooltip` 等基本配置。
4.  **核心逻辑**:
    *   `loadData()`: 调用 `fetchMonitorData` 获取数据, 成功后更新 `monitorData` 并调用 `initCharts` 更新图表数据。
    *   `onMounted` 中首次调用 `loadData`。

---

## 第五部分: 样式层 (`<style scoped>`)

```css
.warehouse-dashboard-v2 {
  padding: 20px;
}
.kpi-card .el-card__body {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.kpi-icon {
  font-size: 3em;
}
.kpi-text {
  text-align: right;
}
.kpi-value {
  font-size: 1.5em;
  font-weight: bold;
}
.chart-container {
  width: 100%;
  height: 300px;
}
```

---

## 第六部分: 路由配置 (`vueGLM/src/router/index.ts`)

```typescript
{
  path: '/warehouse/dashboard-v2',
  name: 'WarehouseDashboardV2',
  component: () => import('@/views/warehouse/WarehouseDashboardV2.vue'),
  meta: { title: '仓库监控V2' }
}
```