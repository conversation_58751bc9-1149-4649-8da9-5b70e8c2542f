### AI Prompt (生成 交易管理 v2 组件 - 最终审核版)

**角色**: 你是一位顶级的、注重代码质量和最佳实践的 Vue.js 前端开发工程师，精通使用 Vue 3 Composition API (`<script setup>`)、TypeScript 和 Element Plus 组件库。

**任务**: 请严格按照以下需求，创建完整的、功能齐全、代码健壮的 Vue 单文件组件（`.vue` 文件），用于交易管理相关页面。组件必须使用 Vue 3 Composition API 和 TypeScript，遵循最佳实践和代码规范。

---

**第一部分：核心原则**

1.  **类型安全**: 整个组件必须使用 TypeScript (`lang="ts"`)。
2.  **前后端分离**: 所有数据交互都必须通过一个抽象的 API 服务层来完成。
3.  **数据一致性**: 确保交易数据的准确性和一致性，实现交易流程的完整闭环。
4.  **可追溯性**: 实现交易记录的完整跟踪和审计日志。

---

**第二部分：依赖项 - API 服务层**

API 文件路径: `src/api/transaction.ts`

1.  **定义接口**: `TransactionItem`, `WarehouseTxnItem`, `TransactionSearchParams`, `ApiResponse` 等。
2.  **实现API函数**: 
    * `fetchTransactionList(params: TransactionSearchParams): Promise<ApiResponse<{ list: TransactionItem[], total: number }>>`: 获取交易列表，支持筛选和分页。
    * `getTransactionDetail(id: string | number): Promise<ApiResponse<TransactionItem>>`: 获取交易详情。
    * `createTransaction(data: Partial<TransactionItem>): Promise<ApiResponse<TransactionItem>>`: 创建新交易。
    * `updateTransaction(data: TransactionItem): Promise<ApiResponse<TransactionItem>>`: 更新交易信息。
    * `updateTransactionStatus(id: string | number, status: string): Promise<ApiResponse<void>>`: 更新交易状态。
    * `fetchWarehouseTxnList(params: TransactionSearchParams): Promise<ApiResponse<{ list: WarehouseTxnItem[], total: number }>>`: 获取仓库交易列表。
    * `getWarehouseTxnDetail(id: string | number): Promise<ApiResponse<WarehouseTxnItem>>`: 获取仓库交易详情。
    * `createWarehouseTxn(data: Partial<WarehouseTxnItem>): Promise<ApiResponse<WarehouseTxnItem>>`: 创建仓库交易。
    * `exportTransactionData(params: TransactionSearchParams): Promise<ApiResponse<void>>`: 导出交易数据。

---

**第三部分：交易信息页面模板布局**

1.  **主容器 (`el-card`)**: 
    * 卡片头部标题为"交易信息管理"，右侧有"创建交易"和"导出"按钮。
2.  **筛选区域**: 
    * 包含交易编号、交易类型、状态、交易时间范围、客户等筛选条件。
    * 包含"重置"和"查询"按钮。
3.  **交易列表 (`el-table`)**: 
    * 包含 `stripe`、`border`、`highlight-current-row` 属性。
    * 列定义: 
      - 选择框: `type="selection"`, `width="55"`
      - 序号: `type="index"`, `width="50"`
      - 交易编号: `prop="transactionNo"`, `label="交易编号"`, `min-width="160"`, `show-overflow-tooltip`
      - 类型: `prop="type"`, `label="类型"`, `width="100"`, `formatter` 转换显示文本
      - 客户: `prop="customerName"`, `label="客户"`, `min-width="160"`, `show-overflow-tooltip`
      - 金额: `prop="amount"`, `label="金额"`, `width="120"`, `formatter` 格式化金额
      - 状态: `prop="status"`, `label="状态"`, `width="100"`, `formatter` 转换显示文本并添加样式
      - 交易时间: `prop="transactionTime"`, `label="交易时间"`, `width="180"`, `formatter` 格式化日期
      - 操作: `label="操作"`, `width="200"`, `fixed="right"`, 包含多个按钮
4.  **操作列**: 
    * 包含详情、编辑、更新状态、查看凭证等按钮。
5.  **分页**: 居右显示。
6.  **对话框**: 
    * 新增/编辑交易对话框，包含交易基本信息、商品明细、付款信息等表单。
    * 交易详情对话框，使用 `el-descriptions` 展示详细信息和商品明细。
    * 状态更新对话框，包含状态选择和备注输入。

---

**第四部分：仓库交易页面模板布局**

1.  **主容器 (`el-card`)**: 
    * 卡片头部标题为"仓库交易管理"，右侧有"创建入库/出库单"和"导出"按钮。
2.  **筛选区域**: 
    * 包含单据编号、交易类型(入库/出库)、仓库、状态、交易时间范围等筛选条件。
    * 包含"重置"和"查询"按钮。
3.  **仓库交易列表 (`el-table`)**: 
    * 包含 `stripe`、`border`、`highlight-current-row` 属性。
    * 列定义: 
      - 选择框: `type="selection"`, `width="55"`
      - 序号: `type="index"`, `width="50"`
      - 单据编号: `prop="docNo"`, `label="单据编号"`, `min-width="160"`, `show-overflow-tooltip`
      - 交易类型: `prop="type"`, `label="交易类型"`, `width="100"`, `formatter` 转换显示文本
      - 仓库: `prop="warehouseName"`, `label="仓库"`, `min-width="120"`, `show-overflow-tooltip`
      - 供应商/客户: `prop="partnerName"`, `label="供应商/客户"`, `min-width="160"`, `show-overflow-tooltip`
      - 状态: `prop="status"`, `label="状态"`, `width="100"`, `formatter` 转换显示文本并添加样式
      - 交易时间: `prop="transactionTime"`, `label="交易时间"`, `width="180"`, `formatter` 格式化日期
      - 操作: `label="操作"`, `width="200"`, `fixed="right"`, 包含多个按钮
4.  **操作列**: 
    * 包含详情、编辑、审核、查看凭证等按钮。
5.  **分页**: 居右显示。
6.  **对话框**: 
    * 新增/编辑入库/出库单对话框，包含基本信息、商品明细、仓库信息等表单。
    * 仓库交易详情对话框，展示详细信息和商品明细。
    * 审核对话框，包含审核结果和意见输入。

---

**第五部分：脚本逻辑实现**

1.  **状态管理**: 管理各页面的筛选表单、分页、表格数据、表单状态和对话框显示。
2.  **核心逻辑**: 
    * `fetchTransactionData()`: 调用 API 获取交易数据。
    * `fetchWarehouseTxnData()`: 调用 API 获取仓库交易数据。
    * `handleCreateTransaction()`: 处理创建交易操作。
    * `handleCreateWarehouseTxn(type)`: 处理创建入库/出库单操作。
    * `handleEdit(row)`: 处理编辑操作。
    * `handleStatusChange(row, status)`: 处理状态变更。
    * `handleAudit(row)`: 处理审核操作。
    * `handleExport()`: 导出数据。
3.  **辅助函数**: 
    * 格式化日期、金额格式化、状态转换等。

---

**第六部分：样式**

1.  使用 `scoped` 样式隔离。
2.  优化表格和表单的布局和间距，确保良好的视觉层次。
3.  为不同类型和状态的交易添加不同的样式标识（如标签颜色）。
4.  确保响应式设计，适应不同屏幕尺寸，特别优化移动设备的显示。
5.  优化商品明细表格的样式，确保数据清晰易读。
6.  为表单元素添加适当的验证反馈样式。
7.  确保按钮、输入框等交互元素有明确的悬停和激活状态。

**第七部分：路由配置**

```typescript
// src/router/index.ts
import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router';
import TransactionManagement from '@/views/transaction/TransactionManagement.vue';
import WarehouseTransaction from '@/views/transaction/WarehouseTransaction.vue';
import TransactionDetail from '@/views/transaction/TransactionDetail.vue';
import WarehouseTxnDetail from '@/views/transaction/WarehouseTxnDetail.vue';

const routes: Array<RouteRecordRaw> = [
  {
    path: '/transaction',
    name: 'TransactionManagement',
    component: TransactionManagement,
    meta: {
      title: '交易信息管理',
      requiresAuth: true,
      permissions: ['transaction.view']
    }
  },
  {
    path: '/transaction/detail/:id',
    name: 'TransactionDetail',
    component: TransactionDetail,
    meta: {
      title: '交易详情',
      requiresAuth: true,
      permissions: ['transaction.view']
    },
    props: true
  },
  {
    path: '/warehouse-transaction',
    name: 'WarehouseTransaction',
    component: WarehouseTransaction,
    meta: {
      title: '仓库交易管理',
      requiresAuth: true,
      permissions: ['warehouseTxn.view']
    }
  },
  {
    path: '/warehouse-transaction/detail/:id',
    name: 'WarehouseTxnDetail',
    component: WarehouseTxnDetail,
    meta: {
      title: '仓库交易详情',
      requiresAuth: true,
      permissions: ['warehouseTxn.view']
    },
    props: true
  }
];
```

1.  使用 `scoped` 样式隔离。
2.  优化表格和表单的布局和间距。
3.  为不同类型和状态的交易添加不同的样式标识。
4.  确保响应式设计，适应不同屏幕尺寸。
5.  优化商品明细表格的样式。