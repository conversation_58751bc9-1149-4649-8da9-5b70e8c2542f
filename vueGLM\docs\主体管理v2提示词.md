### AI Prompt (生成 主体管理V2 (EntityManagementV2.vue) 组件 - 最终审核版)

**角色**: 你是一位顶级的、注重代码质量和最佳实践的 Vue.js 前端开发工程师，精通使用 Vue 3 Composition API (`<script setup>`)、TypeScript 和 Element Plus 组件库。

**任务**: 请严格按照以下需求，创建一个完整的、功能齐全、代码健壮的 Vue 单文件组件（`.vue` 文件），用于主体管理页面。组件必须使用 Vue 3 Composition API 和 TypeScript，遵循最佳实践和代码规范。

---

**第一部分：核心原则**

1.  **类型安全**: 整个组件必须使用 TypeScript (`lang="ts"`)。
2.  **前后端分离**: 所有数据交互都必须通过一个抽象的 API 服务层来完成。
3.  **业务逻辑完整**: 实现主体信息的全生命周期管理，包括创建、查询、更新、删除和状态管理等环节。

---

---

**第二部分：依赖项 - API 服务层**

API 文件路径: `src/api/entityV2.ts`

1.  **定义接口**: `EntityItem`, `SearchParams`, `ApiResponse` 等。

```typescript
// src/api/entityV2.ts

// 主体信息接口
export interface EntityItem {
  id: string;
  entityName: string; // 主体名称
  entityType: '冷库' | '运输企业' | '个体户' | '合作社';
  contactPerson: string; // 联系人
  contactPhone: string; // 联系电话
  address: string; // 详细地址
  status: 'active' | 'inactive' | 'pending'; // 状态: active-已启用, inactive-已停用, pending-待审核
  createTime: string; // 创建时间, 格式: 'YYYY-MM-DD HH:mm:ss'
  creditCode?: string; // 社会统一信用代码
}

// 搜索参数接口
export interface SearchParams {
  entityName?: string;
  entityType?: '' | '冷库' | '运输企业' | '个体户' | '合作社';
  status?: '' | 'active' | 'inactive' | 'pending';
  dateRange?: string[]; // [startTime, endTime]
  page: number;
  pageSize: number;
}

// API 响应接口
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}
```

2.  **实现API函数**: 
    * `fetchEntityList(params: SearchParams): Promise<ApiResponse<{ list: EntityItem[]; total: number }>>`: 获取主体列表，支持筛选和分页。
    * `getEntityDetail(id: string): Promise<ApiResponse<EntityItem | undefined>>`: 获取主体详情。
    * `createEntity(data: Omit<EntityItem, 'id' | 'createTime'>): Promise<ApiResponse<EntityItem>>`: 创建新主体。
    * `updateEntity(data: EntityItem): Promise<ApiResponse<EntityItem>>`: 更新主体信息。
    * `deleteEntity(id: string): Promise<ApiResponse<null>>`: 删除主体。
    * `updateEntityStatus(id: string, status: EntityItem['status']): Promise<ApiResponse<null>>`: 更新主体状态。

---

---

**第三部分：组件模板详细布局**

1.  **整体布局**

*   根元素为 `<div class="entity-management-v2">`。
*   使用 `<el-card>` 作为主容器, `shadow="never"`。

2.  **卡片头部**

*   使用 `flex` 布局, `justify-content: space-between`。
*   左侧为标题 "主体管理"。
*   右侧包含 "新增主体" (`type="primary"`) 和 "导出" 两个 `el-button`。

3.  **搜索区域**

*   使用 `<div class="search-section">` 包裹 `<el-form>`。
*   `<el-form>` 设置 `:inline="true"`。
*   包含以下表单项:
    1.  **主体名称**: `el-input`, `v-model="searchForm.entityName"`, `placeholder="请输入主体名称"`, `clearable`。
    2.  **主体类型**: `el-select`, `v-model="searchForm.entityType"`, `placeholder="请选择主体类型"`, `clearable`。
        *   `el-option`: 冷库, 运输企业, 个体户, 合作社。
    3.  **状态**: `el-select`, `v-model="searchForm.status"`, `placeholder="请选择状态"`, `clearable`。
        *   `el-option`: 已启用, 已停用, 待审核。
    4.  **创建时间**: `el-date-picker`, `v-model="searchForm.dateRange"`, `type="daterange"`。
    5.  **操作按钮**: "查询" (`type="primary"`) 和 "重置" `el-button`。

4.  **数据表格**

*   使用 `<el-table>`, `v-loading="loading"`, 数据绑定到 `tableData`, `border`, `stripe`。
*   包含以下 `<el-table-column>`:
    *   `type="selection"`
    *   `type="index"`, `label="序号"`
    *   `主体名称` (`prop="entityName"`)
    *   `主体类型` (`prop="entityType"`)
    *   `联系人` (`prop="contactPerson"`)
    *   `联系电话` (`prop="contactPhone"`)
    *   `状态`: 使用自定义模板和 `el-tag` (已启用-success, 已停用-info, 待审核-warning)。
    *   `创建时间` (`prop="createTime"`)
    *   `操作` (`fixed="right"`, `width="280"`):
        *   `el-button` `link` `type="primary"`: "查看", "编辑"。
        *   `el-button` `link` `type="primary"`: "启用" (`v-if="row.status === 'inactive'"`), "停用" (`v-if="row.status === 'active'"`).
        *   `el-button` `link` `type="primary"` `v-if="row.auditStatus === 'approved'"`: "仓库"。
        *   `el-button` `link` `type="primary"` `v-if="row.auditStatus === 'approved'"`: "库存"。

5.  **分页**

*   使用 `<div class="pagination-wrapper">` 包裹 `<el-pagination>`。
*   `<el-pagination>` 居右显示, 包含 `total`, `sizes`, `prev`, `pager`, `next`, `jumper`。

6.  **新增/编辑对话框**

*   使用 `<el-dialog>`, 根据模式 (新增/编辑) 显示不同标题。
*   内部使用 `<el-form>` 和 `el-form-item` 布局所有 `EntityItem` 中的可编辑字段 (如: 主体名称, 类型, 联系人, 电话, 地址, 信用代码, 状态等)。
*   包含表单校验规则 (`rules`)。
*   底部有 "取消" 和 "确定" 按钮。

---

---

**第四部分：脚本逻辑实现**

1.  **状态管理**: 管理筛选表单、分页、表格数据、多选状态和对话框显示。
2.  **核心逻辑**: 
    * `loadData()`: 调用 API 获取列表数据。
    * `handleSearch()`, `resetSearch()`: 处理搜索和重置。
    * `handleSizeChange()`, `handleCurrentChange()`: 处理分页。
    * `showAddDialog()`, `showEditDialog(row)`: 打开新增/编辑对话框并初始化数据。
    * `handleDialogSubmit()`: 处理对话框提交，调用 `createEntity` 或 `updateEntity`。
    * `handleDelete(row)`: 处理删除，使用 `ElMessageBox.confirm`。
    * `handleStatusChange(row)`: 处理状态变更。
3.  **辅助函数**: 
    * `statusText()`, `statusTagType()`: 辅助函数，转换状态显示。

---

---

**第五部分：样式**

1.  使用 `scoped` 样式隔离。
2.  优化表格和表单的布局和间距，确保良好的视觉层次。
3.  为状态标签和操作按钮添加不同样式以提高辨识度（如颜色标签）。
4.  确保响应式设计，适应不同屏幕尺寸，特别优化移动设备的显示。
5.  优化卡片和表格的样式，提高数据可读性。
6.  为交互元素添加悬停和激活状态样式。

---

---

**第六部分：路由配置**

```typescript
// src/router/index.ts
import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router';
import EntityManagementV2 from '@/views/entity/EntityManagementV2.vue';

const routes: Array<RouteRecordRaw> = [
  {
    path: '/entity/management-v2',
    name: 'EntityManagementV2',
    component: EntityManagementV2,
    meta: {
      title: '主体管理V2',
      requiresAuth: true,
      permissions: ['entity.view']
    }
  }
];
```
