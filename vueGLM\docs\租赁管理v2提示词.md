# 提示词: 生成 vueGLM 租赁管理模块 V2

## 第一部分: 核心原则与目标

1.  **目标**: 创建一个包含 **共享冷库** (`SharedColdStorageV2.vue`) 和 **我的租赁** (`MyRentalsV2.vue`) 两个核心页面的租赁管理模块。
2.  **技术栈**: Vue 3 (Composition API, `<script setup>`), TypeScript, Element Plus, Vue Router。
3.  **代码风格**: 遵循项目现有代码风格。
4.  **数据模拟**: 所有后端数据请求都必须通过一个独立的 API 文件 (`vueGLM/src/api/rentalV2.ts`) 来模拟, **不得**引入第三方库。
5.  **像素级还原**: 最终生成的页面应与本提示词描述高度一致。

---

## 第二部分: API 层 (`vueGLM/src/api/rentalV2.ts`)

### 1. 接口定义

```typescript
// vueGLM/src/api/rentalV2.ts

export interface SharedStorageItem {
  id: string;
  name: string;
  location: string;
  area: number; // 面积 (㎡)
  price: number; // 价格 (元/天)
  status: 'available' | 'rented'; // 可租赁 / 已租出
  publishTime: string;
}

export interface MyRentalItem {
    id: string;
    contractNo: string;
    storageName: string;
    startDate: string;
    endDate: string;
    totalAmount: number;
    status: 'active' | 'expired' | 'terminated'; // 履约中 / 已到期 / 已终止
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}
```

### 2. 模拟数据与 API 函数

*   创建 `mockSharedStorageDatabase` 和 `mockMyRentalsDatabase` 模拟数据库。
*   实现 `fetchSharedStorageList(params)`, `fetchMyRentalsList(params)` 等模拟 API 函数。

---

## 第三部分: 共享冷库页面 (`SharedColdStorageV2.vue`)

### 1. 模板

*   **主容器**: `<el-card>`。
*   **卡片头部**: 标题 "共享冷库", 右侧 "发布冷库"(`type="primary"`) 按钮。
*   **筛选区域**: `冷库名称`, `位置`, `状态`。
*   **数据表格**: `<el-table>` `border` `stripe`
    *   `冷库名称`, `位置`, `面积(㎡)`, `价格(元/天)`, `状态`(el-tag), `发布时间`, `操作`。
    *   `操作`: "详情", "立即预订" (`v-if="available"`)。
*   **分页**: `<el-pagination>`, 居右。

---

## 第四部分: 我的租赁页面 (`MyRentalsV2.vue`)

### 1. 模板

*   **主容器**: `<el-card>`。
*   **卡片头部**: 标题 "我的租赁"。
*   **筛选区域**: `合同状态`。
*   **数据表格**: `<el-table>` `border` `stripe`
    *   `合同编号`, `租赁冷库`, `起租日期`, `到期日期`, `总金额`, `合同状态`(el-tag), `操作`。
    *   `操作`: "查看合同", "申请续租" (`v-if="active"`), "退租" (`v-if="active"`)。
*   **分页**: `<el-pagination>`, 居右。

---

## 第五部分: 样式层 (`<style scoped>`)

```css
.page-container { padding: 20px; }
.search-section { margin-bottom: 20px; }
.pagination-wrapper { margin-top: 20px; display: flex; justify-content: flex-end; }
```

---

## 第六部分: 路由配置 (`vueGLM/src/router/index.ts`)

```typescript
{
  path: '/rental/shared-v2',
  name: 'SharedColdStorageV2',
  component: () => import('@/views/rental/SharedColdStorageV2.vue'),
  meta: { title: '共享冷库V2' }
},
{
  path: '/rental/my-rentals-v2',
  name: 'MyRentalsV2',
  component: () => import('@/views/rental/MyRentalsV2.vue'),
  meta: { title: '我的租赁V2' }
}
```