# 提示词: 生成 vueGLM 系统管理模块 V2

## 第一部分: 核心原则与目标

1.  **目标**: 创建 **用户管理** (`UserManagementV2.vue`) 和 **角色管理** (`RoleManagementV2.vue`) 两个核心页面。
2.  **技术栈**: Vue 3 (Composition API, `<script setup>`), TypeScript, Element Plus, Vue Router。
3.  **代码风格**: 遵循项目现有代码风格。
4.  **数据模拟**: 所有后端数据请求都必须通过一个独立的 API 文件 (`vueGLM/src/api/systemV2.ts`) 来模拟, **不得**引入第三方库。
5.  **像素级还原**: 最终生成的页面应与本提示词描述高度一致。

---

## 第二部分: API 层 (`vueGLM/src/api/systemV2.ts`)

### 1. 接口定义

```typescript
// vueGLM/src/api/systemV2.ts

export interface UserItem {
  id: string;
  username: string;
  realName: string;
  role: '超级管理员' | '普通用户' | '审计员';
  phone: string;
  status: 'active' | 'inactive';
  lastLoginTime: string;
}

export interface RoleItem {
    id: string;
    roleName: string;
    permissions: string[]; // 权限标识符数组
    userCount: number;
    createTime: string;
}

export interface PermissionNode { // 用于权限树
    id: string;
    label: string;
    children?: PermissionNode[];
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}
```

### 2. 模拟数据与 API 函数

*   创建 `mockUserDatabase`, `mockRoleDatabase`, `mockPermissionTree` 模拟数据库。
*   实现 `fetchUserList(params)`, `createUser(data)`, `updateUser(data)`, `deleteUser(id)`。
*   实现 `fetchRoleList(params)`, `getRolePermissions(roleId)`, `updateRolePermissions(roleId, permissions)`。
*   实现 `fetchPermissionTree()`。

---

## 第三部分: 用户管理页面 (`UserManagementV2.vue`)

### 1. 模板

*   **主容器**: `<el-card>`。
*   **卡片头部**: 标题 "用户管理", 右侧 "新增用户"(`type="primary"`)。
*   **筛选区域**: `用户名`, `姓名`, `角色`, `状态`。
*   **数据表格**: `<el-table>` `border` `stripe`
    *   `用户名`, `姓名`, `角色`, `联系电话`, `状态`(el-tag), `最后登录时间`, `操作`。
    *   `操作`: "编辑", "重置密码", "删除"。
*   **分页**: `<el-pagination>`, 居右。
*   **新增/编辑对话框**: 包含用户信息的表单。

---

## 第四部分: 角色管理页面 (`RoleManagementV2.vue`)

### 1. 模板

*   **主容器**: `<el-card>`。
*   **卡片头部**: 标题 "角色管理", 右侧 "新增角色"(`type="primary"`)。
*   **数据表格**: `<el-table>` `border` `stripe`
    *   `角色名称`, `关联用户数`, `创建时间`, `操作`。
    *   `操作`: "分配权限", "编辑", "删除"。
*   **分页**: `<el-pagination>`, 居右。
*   **分配权限对话框**:
    *   标题: `为 "${currentRole.roleName}" 分配权限`。
    *   使用 `<el-tree>` 组件展示权限树, `show-checkbox`, `node-key="id"`, `default-expand-all`。
    *   底部有 "取消" 和 "保存" 按钮。

---

## 第五部分: 样式层 (`<style scoped>`)

```css
.page-container { padding: 20px; }
.search-section { margin-bottom: 20px; }
.pagination-wrapper { margin-top: 20px; display: flex; justify-content: flex-end; }
```

---

## 第六部分: 路由配置 (`vueGLM/src/router/index.ts`)

```typescript
{
  path: '/system/user-v2',
  name: 'UserManagementV2',
  component: () => import('@/views/system/UserManagementV2.vue'),
  meta: { title: '用户管理V2' }
},
{
  path: '/system/role-v2',
  name: 'RoleManagementV2',
  component: () => import('@/views/system/RoleManagementV2.vue'),
  meta: { title: '角色管理V2' }
}
```