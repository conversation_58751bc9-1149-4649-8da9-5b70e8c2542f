# 提示词: 生成 vueGLM 导航菜单与路由 V2

## 第一部分: 核心原则与目标

1.  **目标**: 生成一份结构清晰、类型安全的 **菜单配置文件** (`menu.ts`) 和一份与之完全对应的 **路由配置文件** (`router.ts`)。
2.  **技术栈**: TypeScript, Vue Router。
3.  **代码风格**: 遵循项目现有代码风格, 确保代码清晰、可维护性高。
4.  **单一事实来源**: `menu.ts` 应作为定义导航结构的唯一来源, `router.ts` 根据 `menu.ts` 动态生成路由, 确保两者一致性。

---

## 第二部分: 菜单结构定义 (`vueGLM/src/config/menu.ts`)

### 1. 接口定义

```typescript
// vueGLM/src/config/menu.ts

export interface MenuItem {
  path: string;
  name: string; // 路由名称, 应唯一
  meta: {
    title: string;
    icon?: string; // Element Plus 图标名称
  };
  children?: MenuItem[];
}
```

### 2. 菜单数据

*   请根据以下结构, 创建一个名为 `menuData` 的 `MenuItem[]` 类型的常量并导出。
*   **一级菜单**:
    *   首页 (`/dashboard`)
    *   主体管理 (`/entity`)
    *   交易管理 (`/transaction`)
        *   **子菜单**: 出入库管理 (`/transaction/warehouse`), 交易流水 (`/transaction/flow`)
    *   库存管理 (`/inventory`)
        *   **子菜单**: 库存列表 (`/inventory/list`), 库存流水 (`/inventory/flow`)
    *   租赁管理 (`/rental`)
        *   **子菜单**: 共享冷库 (`/rental/shared`), 我的租赁 (`/rental/my-rentals`)
    *   政策管理 (`/policy`)
        *   **子菜单**: 政策列表 (`/policy/list`), 我的申请 (`/policy/my-applications`)
    *   应急管理 (`/emergency`)
    *   系统管理 (`/system`)
        *   **子菜单**: 用户管理 (`/system/users`), 角色管理 (`/system/roles`), 系统日志 (`/system/logs`)
    *   设置 (`/settings`)

---

## 第三部分: 路由生成逻辑 (`vueGLM/src/router/index.ts`)

### 1. 核心逻辑

*   导入 `menuData` from `../config/menu.ts`。
*   编写一个递归函数 `generateRoutes(menuItems: MenuItem[]): RouteRecordRaw[]`, 该函数接收菜单数据, 将其转换为 Vue Router 的路由记录数组。
*   每个 `MenuItem` 都应转换为一个 `RouteRecordRaw` 对象。
    *   `path`, `name`, `meta` 直接映射。
    *   `component` 属性使用 `() => import(...)` 动态导入对应的 `.vue` 文件 (路径根据 `path` 推断)。
    *   如果 `MenuItem` 包含 `children`, 则递归调用 `generateRoutes` 处理子路由。
*   最终导出的 `routes` 数组应包含登录页、404 页以及通过 `generateRoutes(menuData)` 生成的应用主路由。

### 2. 最终路由结构示例

```typescript
const routes: RouteRecordRaw[] = [
  { path: '/login', component: ... },
  { 
    path: '/', 
    component: Layout, // 主布局
    children: generateRoutes(menuData)
  },
  { path: '/:pathMatch(.*)*', name: 'NotFound', component: ... },
];
```

---

## 第四部分: 任务

1.  **创建 `vueGLM/src/config/menu.ts`**: 包含 `MenuItem` 接口和 `menuData` 导出。
2.  **创建 `vueGLM/src/router/index.ts`**: 包含 `generateRoutes` 函数和最终的 `routes` 配置。
3.  **占位符组件**: 为上述所有路由路径创建空的 `.vue` 文件 (例如 `views/dashboard/index.vue`), 以确保路由导入能正常工作。